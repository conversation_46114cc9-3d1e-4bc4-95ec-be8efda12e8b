import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Layout from './components/Layout';
import ErrorBoundary from './components/ErrorBoundary';
import Login from './pages/Login';
import AdminDashboard from './pages/admin/Dashboard';
import AdminStudents from './pages/admin/Students';
import AdminTeachers from './pages/admin/Teachers';
import AdminClasses from './pages/admin/Classes';
import AdminAttendance from './pages/admin/Attendance';
import AdminReports from './pages/admin/Reports';
import TeacherDashboard from './pages/teacher/DashboardFixed';
import TeacherAttendance from './pages/teacher/Attendance';
import TeacherScores from './pages/teacher/Scores';
import TeacherStudents from './pages/teacher/Students';
import TeacherClasses from './pages/teacher/Classes';
import StudentDashboard from './pages/student/Dashboard';
import StudentAttendance from './pages/student/Attendance';
import StudentScores from './pages/student/Scores';
import StudentClasses from './pages/student/Classes';
import AdminSettings from './pages/admin/Settings';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function AppRoutes() {
  const { user, isAuthenticated } = useAuth();

  const getDashboardRoute = () => {
    if (!isAuthenticated || !user) return '/login';

    switch (user.role) {
      case 'admin':
        return '/admin';
      case 'teacher':
        return '/teacher';
      case 'student':
        return '/student';
      default:
        return '/login';
    }
  };

  return (
    <Routes>
      <Route path="/login" element={<Login />} />

      {/* Redirect root to appropriate dashboard */}
      <Route path="/" element={<Navigate to={getDashboardRoute()} replace />} />

      {/* Admin Routes */}
      <Route path="/admin" element={
        <ProtectedRoute roles={['admin']}>
          <Layout>
            <AdminDashboard />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/admin/students" element={
        <ProtectedRoute roles={['admin']}>
          <Layout>
            <AdminStudents />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/admin/teachers" element={
        <ProtectedRoute roles={['admin']}>
          <Layout>
            <AdminTeachers />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/admin/classes" element={
        <ProtectedRoute roles={['admin']}>
          <Layout>
            <AdminClasses />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/admin/attendance" element={
        <ProtectedRoute roles={['admin']}>
          <Layout>
            <AdminAttendance />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/admin/reports" element={
        <ProtectedRoute roles={['admin']}>
          <Layout>
            <AdminReports />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/admin/settings" element={
        <ProtectedRoute roles={['admin']}>
          <Layout>
            <ErrorBoundary>
              <AdminSettings />
            </ErrorBoundary>
          </Layout>
        </ProtectedRoute>
      } />

      {/* Teacher Routes */}
      <Route path="/teacher" element={
        <ProtectedRoute roles={['teacher']}>
          <Layout>
            <ErrorBoundary>
              <TeacherDashboard />
            </ErrorBoundary>
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/teacher/attendance" element={
        <ProtectedRoute roles={['teacher']}>
          <Layout>
            <TeacherAttendance />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/teacher/scores" element={
        <ProtectedRoute roles={['teacher']}>
          <Layout>
            <TeacherScores />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/teacher/students" element={
        <ProtectedRoute roles={['teacher']}>
          <Layout>
            <TeacherStudents />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/teacher/classes" element={
        <ProtectedRoute roles={['teacher']}>
          <Layout>
            <TeacherClasses />
          </Layout>
        </ProtectedRoute>
      } />

      {/* Student Routes */}
      <Route path="/student" element={
        <ProtectedRoute roles={['student']}>
          <Layout>
            <StudentDashboard />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/student/attendance" element={
        <ProtectedRoute roles={['student']}>
          <Layout>
            <StudentAttendance />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/student/scores" element={
        <ProtectedRoute roles={['student']}>
          <Layout>
            <StudentScores />
          </Layout>
        </ProtectedRoute>
      } />
      <Route path="/student/classes" element={
        <ProtectedRoute roles={['student']}>
          <Layout>
            <StudentClasses />
          </Layout>
        </ProtectedRoute>
      } />

      {/* Catch all route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router future={{ v7_relativeSplatPath: true }}>
          <div className="App">
            <AppRoutes />
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
                success: {
                  duration: 3000,
                  theme: {
                    primary: '#4aed88',
                  },
                },
              }}
            />
          </div>
        </Router>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
