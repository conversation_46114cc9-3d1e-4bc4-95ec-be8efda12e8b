import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { attendanceAPI } from '../../utils/api';
import { Calendar, TrendingUp, Check, X, Clock, Filter } from 'lucide-react';

export default function StudentAttendance() {
  const { user } = useAuth();
  const [dateRange, setDateRange] = useState('month');
  const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().slice(0, 7));
  
  const studentId = user?.student?.id;

  // Calculate date range based on selection
  const getDateRange = () => {
    const now = new Date();
    let startDate, endDate;

    switch (dateRange) {
      case 'week':
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        startDate = startOfWeek.toISOString().split('T')[0];
        endDate = now.toISOString().split('T')[0];
        break;
      case 'month':
        const [year, month] = selectedMonth.split('-');
        startDate = `${year}-${month}-01`;
        const lastDay = new Date(year, month, 0).getDate();
        endDate = `${year}-${month}-${lastDay.toString().padStart(2, '0')}`;
        break;
      case 'semester':
        // Assuming semester starts in September
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth();
        if (currentMonth >= 8) { // September onwards
          startDate = `${currentYear}-09-01`;
          endDate = `${currentYear + 1}-01-31`;
        } else {
          startDate = `${currentYear}-02-01`;
          endDate = `${currentYear}-06-30`;
        }
        break;
      default:
        startDate = null;
        endDate = null;
    }

    return { startDate, endDate };
  };

  const { startDate, endDate } = getDateRange();

  // Get student attendance data
  const { data: attendanceData, isLoading } = useQuery({
    queryKey: ['student-attendance', studentId, startDate, endDate],
    queryFn: () => attendanceAPI.getByStudent(studentId, { startDate, endDate }),
    enabled: !!studentId,
  });

  const attendance = attendanceData?.attendance || [];

  // Calculate statistics
  const getAttendanceStats = () => {
    const total = attendance.length;
    const present = attendance.filter(record => record.status === 'present').length;
    const absent = attendance.filter(record => record.status === 'absent').length;
    const late = attendance.filter(record => record.status === 'late').length;
    const percentage = total > 0 ? Math.round((present / total) * 100) : 0;

    return { total, present, absent, late, percentage };
  };

  const stats = getAttendanceStats();

  const getStatusColor = (status) => {
    switch (status) {
      case 'present': return 'text-green-600 bg-green-100';
      case 'absent': return 'text-red-600 bg-red-100';
      case 'late': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'present': return <Check className="w-4 h-4" />;
      case 'absent': return <X className="w-4 h-4" />;
      case 'late': return <Clock className="w-4 h-4" />;
      default: return null;
    }
  };

  if (!user?.student) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 mb-2">Student profile not found</div>
          <div className="text-sm text-gray-500">Please contact administrator</div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">My Attendance</h1>
        <p className="text-gray-600">Track your attendance record and statistics</p>
      </div>

      {/* Date Range Filter */}
      <div className="card">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-gray-400" />
            <span className="text-sm font-medium text-gray-700">View:</span>
          </div>
          
          <div className="flex gap-2">
            {[
              { value: 'week', label: 'This Week' },
              { value: 'month', label: 'Month' },
              { value: 'semester', label: 'Semester' },
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => setDateRange(option.value)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  dateRange === option.value
                    ? 'bg-primary-100 text-primary-700 border border-primary-200'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>

          {dateRange === 'month' && (
            <input
              type="month"
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(e.target.value)}
              className="input text-sm"
            />
          )}
        </div>
      </div>

      {/* Attendance Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="card text-center">
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-sm text-gray-600">Total Days</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-green-600">{stats.present}</div>
          <div className="text-sm text-gray-600">Present</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-red-600">{stats.absent}</div>
          <div className="text-sm text-gray-600">Absent</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-yellow-600">{stats.late}</div>
          <div className="text-sm text-gray-600">Late</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.percentage}%</div>
          <div className="text-sm text-gray-600">Attendance Rate</div>
        </div>
      </div>

      {/* Attendance Progress */}
      <div className="card">
        <div className="flex items-center gap-3 mb-4">
          <TrendingUp className="w-5 h-5 text-primary-600" />
          <h3 className="text-lg font-medium text-gray-900">Attendance Progress</h3>
        </div>
        
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span>Overall Attendance Rate</span>
            <span className="font-medium">{stats.percentage}%</span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-300 ${
                stats.percentage >= 90 ? 'bg-green-500' :
                stats.percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
              style={{ width: `${stats.percentage}%` }}
            ></div>
          </div>
          
          <div className="text-xs text-gray-500">
            {stats.percentage >= 90 ? 'Excellent attendance!' :
             stats.percentage >= 75 ? 'Good attendance, keep it up!' :
             'Attendance needs improvement'}
          </div>
        </div>
      </div>

      {/* Recent Attendance Records */}
      <div className="card">
        <div className="flex items-center gap-3 mb-4">
          <Calendar className="w-5 h-5 text-primary-600" />
          <h3 className="text-lg font-medium text-gray-900">Recent Records</h3>
        </div>

        {attendance.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No attendance records found for the selected period
          </div>
        ) : (
          <div className="space-y-3">
            {attendance
              .sort((a, b) => new Date(b.date) - new Date(a.date))
              .slice(0, 10)
              .map((record, index) => (
                <div key={`${record.date}-${record.class_name}-${index}`} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-3">
                      <div className="text-sm font-medium text-gray-900">
                        {new Date(record.date).toLocaleDateString('en-US', {
                          weekday: 'short',
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </div>
                      <div className="text-sm text-gray-600">
                        {record.class_name} - {record.subject}
                      </div>
                    </div>
                    {record.notes && (
                      <div className="text-xs text-gray-500 mt-1">{record.notes}</div>
                    )}
                  </div>
                  
                  <div className={`flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium ${getStatusColor(record.status)}`}>
                    {getStatusIcon(record.status)}
                    {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                  </div>
                </div>
              ))}
          </div>
        )}
      </div>

      {/* Attendance by Subject */}
      {attendance.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Attendance by Subject</h3>
          
          <div className="space-y-3">
            {Object.entries(
              attendance.reduce((acc, record) => {
                const subject = record.subject;
                if (!acc[subject]) {
                  acc[subject] = { total: 0, present: 0, absent: 0, late: 0 };
                }
                acc[subject].total++;
                acc[subject][record.status]++;
                return acc;
              }, {})
            ).map(([subject, subjectStats]) => {
              const percentage = Math.round((subjectStats.present / subjectStats.total) * 100);
              return (
                <div key={subject} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium text-gray-900">{subject}</span>
                    <span className="text-sm text-gray-600">{percentage}%</span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div
                      className={`h-2 rounded-full ${
                        percentage >= 90 ? 'bg-green-500' :
                        percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  
                  <div className="flex justify-between text-xs text-gray-600">
                    <span>Present: {subjectStats.present}</span>
                    <span>Absent: {subjectStats.absent}</span>
                    <span>Late: {subjectStats.late}</span>
                    <span>Total: {subjectStats.total}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
