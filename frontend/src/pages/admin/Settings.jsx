import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Settings, Save, Plus, Trash2, Edit3 } from 'lucide-react';
import toast from 'react-hot-toast';
import { settingsAPI } from '../../utils/api';

export default function AdminSettings() {
  const [activeTab, setActiveTab] = useState('grades');
  const [editingGrade, setEditingGrade] = useState(null);
  const [editingSection, setEditingSection] = useState(null);
  const [editingSubject, setEditingSubject] = useState(null);
  const [newGrade, setNewGrade] = useState('');
  const [newSection, setNewSection] = useState('');
  const [newSubject, setNewSubject] = useState('');
  const queryClient = useQueryClient();

  // Get settings from API
  const { data: settingsData, isLoading, error } = useQuery({
    queryKey: ['settings'],
    queryFn: () => settingsAPI.getSettings(),
  });

  const settings = settingsData?.data || {};
  const grades = settings.grades || [];
  const sections = settings.sections || [];
  const subjects = settings.subjects || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="text-center py-8">
          <div className="text-red-500 mb-2">Error loading settings</div>
          <div className="text-sm text-gray-500">{error.message}</div>
        </div>
      </div>
    );
  }

  // Mutations for updating settings
  const updateGradesMutation = useMutation({
    mutationFn: (grades) => settingsAPI.updateGrades(grades),
    onSuccess: () => {
      queryClient.invalidateQueries(['settings']);
      toast.success('Grades updated successfully');
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Failed to update grades');
    },
  });

  const updateSectionsMutation = useMutation({
    mutationFn: (sections) => settingsAPI.updateSections(sections),
    onSuccess: () => {
      queryClient.invalidateQueries(['settings']);
      toast.success('Sections updated successfully');
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Failed to update sections');
    },
  });

  const updateSubjectsMutation = useMutation({
    mutationFn: (subjects) => settingsAPI.updateSubjects(subjects),
    onSuccess: () => {
      queryClient.invalidateQueries(['settings']);
      toast.success('Subjects updated successfully');
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Failed to update subjects');
    },
  });

  const handleAddGrade = () => {
    if (newGrade && !grades.includes(newGrade)) {
      const updatedGrades = [...grades, newGrade];
      updateGradesMutation.mutate(updatedGrades);
      setNewGrade('');
    } else {
      toast.error('Grade already exists or is empty');
    }
  };

  const handleDeleteGrade = (grade) => {
    if (grades.length > 1) {
      const updatedGrades = grades.filter(g => g !== grade);
      updateGradesMutation.mutate(updatedGrades);
    } else {
      toast.error('Cannot delete the last grade');
    }
  };

  const handleEditGrade = (oldGrade, newGradeValue) => {
    if (newGradeValue && !grades.includes(newGradeValue)) {
      const updatedGrades = grades.map(g => g === oldGrade ? newGradeValue : g);
      updateGradesMutation.mutate(updatedGrades);
      setEditingGrade(null);
    } else {
      toast.error('Grade already exists or is empty');
    }
  };

  const handleAddSection = () => {
    if (newSection && !sections.includes(newSection.toUpperCase())) {
      const updatedSections = [...sections, newSection.toUpperCase()];
      updateSectionsMutation.mutate(updatedSections);
      setNewSection('');
    } else {
      toast.error('Section already exists or is empty');
    }
  };

  const handleDeleteSection = (section) => {
    if (sections.length > 1) {
      const updatedSections = sections.filter(s => s !== section);
      updateSectionsMutation.mutate(updatedSections);
    } else {
      toast.error('Cannot delete the last section');
    }
  };

  const handleEditSection = (oldSection, newSectionValue) => {
    if (newSectionValue && !sections.includes(newSectionValue.toUpperCase())) {
      const updatedSections = sections.map(s => s === oldSection ? newSectionValue.toUpperCase() : s);
      updateSectionsMutation.mutate(updatedSections);
      setEditingSection(null);
    } else {
      toast.error('Section already exists or is empty');
    }
  };

  const handleAddSubject = () => {
    if (newSubject && !subjects.includes(newSubject)) {
      const updatedSubjects = [...subjects, newSubject];
      updateSubjectsMutation.mutate(updatedSubjects);
      setNewSubject('');
    } else {
      toast.error('Subject already exists or is empty');
    }
  };

  const handleDeleteSubject = (subject) => {
    if (subjects.length > 1) {
      const updatedSubjects = subjects.filter(s => s !== subject);
      updateSubjectsMutation.mutate(updatedSubjects);
    } else {
      toast.error('Cannot delete the last subject');
    }
  };

  const handleEditSubject = (oldSubject, newSubjectValue) => {
    if (newSubjectValue && !subjects.includes(newSubjectValue)) {
      const updatedSubjects = subjects.map(s => s === oldSubject ? newSubjectValue : s);
      updateSubjectsMutation.mutate(updatedSubjects);
      setEditingSubject(null);
    } else {
      toast.error('Subject already exists or is empty');
    }
  };

  const tabs = [
    { id: 'grades', name: 'Grades', icon: Settings },
    { id: 'sections', name: 'Sections', icon: Settings },
    { id: 'subjects', name: 'Subjects', icon: Settings },
    { id: 'general', name: 'General', icon: Settings },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Manage system configuration and preferences</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Grades Tab */}
      {activeTab === 'grades' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Manage Grades</h3>
          <p className="text-sm text-gray-600 mb-6">
            Add, edit, or remove grade levels available in the system.
          </p>

          {/* Add New Grade */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Add New Grade</h4>
            <div className="flex space-x-3">
              <input
                type="text"
                value={newGrade}
                onChange={(e) => setNewGrade(e.target.value)}
                placeholder="Enter grade (e.g., 13, K, Pre-K)"
                className="input flex-1"
              />
              <button onClick={handleAddGrade} className="btn-primary flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span>Add Grade</span>
              </button>
            </div>
          </div>

          {/* Existing Grades */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">Current Grades ({grades.length})</h4>
            {grades.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No grades configured yet. Add your first grade above.
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {grades.map((grade) => (
                <div key={grade} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  {editingGrade === grade ? (
                    <div className="flex items-center space-x-2 flex-1">
                      <input
                        type="text"
                        defaultValue={grade}
                        onBlur={(e) => handleEditGrade(grade, e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            handleEditGrade(grade, e.target.value);
                          }
                        }}
                        className="input text-sm flex-1"
                        autoFocus
                      />
                    </div>
                  ) : (
                    <>
                      <span className="text-sm font-medium text-gray-900">Grade {grade}</span>
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => setEditingGrade(grade)}
                          className="text-gray-400 hover:text-blue-600"
                        >
                          <Edit3 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteGrade(grade)}
                          className="text-gray-400 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </>
                  )}
                </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Sections Tab */}
      {activeTab === 'sections' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Manage Sections</h3>
          <p className="text-sm text-gray-600 mb-6">
            Add, edit, or remove class sections available in the system.
          </p>

          {/* Add New Section */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Add New Section</h4>
            <div className="flex space-x-3">
              <input
                type="text"
                value={newSection}
                onChange={(e) => setNewSection(e.target.value)}
                placeholder="Enter section (e.g., D, E, Alpha)"
                className="input flex-1"
              />
              <button onClick={handleAddSection} className="btn-primary flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span>Add Section</span>
              </button>
            </div>
          </div>

          {/* Existing Sections */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">Current Sections ({sections.length})</h4>
            {sections.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No sections configured yet. Add your first section above.
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {sections.map((section) => (
                <div key={section} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  {editingSection === section ? (
                    <div className="flex items-center space-x-2 flex-1">
                      <input
                        type="text"
                        defaultValue={section}
                        onBlur={(e) => handleEditSection(section, e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            handleEditSection(section, e.target.value);
                          }
                        }}
                        className="input text-sm flex-1"
                        autoFocus
                      />
                    </div>
                  ) : (
                    <>
                      <span className="text-sm font-medium text-gray-900">Section {section}</span>
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => setEditingSection(section)}
                          className="text-gray-400 hover:text-blue-600"
                        >
                          <Edit3 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteSection(section)}
                          className="text-gray-400 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </>
                  )}
                </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Subjects Tab */}
      {activeTab === 'subjects' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Manage Subjects</h3>
          <p className="text-sm text-gray-600 mb-6">
            Add, edit, or remove subjects available in the system.
          </p>

          {/* Add New Subject */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Add New Subject</h4>
            <div className="flex space-x-3">
              <input
                type="text"
                value={newSubject}
                onChange={(e) => setNewSubject(e.target.value)}
                placeholder="Enter subject (e.g., Art, Music, PE)"
                className="input flex-1"
              />
              <button onClick={handleAddSubject} className="btn-primary flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span>Add Subject</span>
              </button>
            </div>
          </div>

          {/* Existing Subjects */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">Current Subjects ({subjects.length})</h4>
            {subjects.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No subjects configured yet. Add your first subject above.
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {subjects.map((subject) => (
                <div key={subject} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  {editingSubject === subject ? (
                    <div className="flex items-center space-x-2 flex-1">
                      <input
                        type="text"
                        defaultValue={subject}
                        onBlur={(e) => handleEditSubject(subject, e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            handleEditSubject(subject, e.target.value);
                          }
                        }}
                        className="input text-sm flex-1"
                        autoFocus
                      />
                    </div>
                  ) : (
                    <>
                      <span className="text-sm font-medium text-gray-900">{subject}</span>
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => setEditingSubject(subject)}
                          className="text-gray-400 hover:text-blue-600"
                        >
                          <Edit3 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteSubject(subject)}
                          className="text-gray-400 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </>
                  )}
                </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* General Tab */}
      {activeTab === 'general' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">School Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="label">School Name</label>
                <input
                  type="text"
                  defaultValue="Hikmaah School"
                  className="input"
                />
              </div>
              <div>
                <label className="label">School Code</label>
                <input
                  type="text"
                  defaultValue="HS001"
                  className="input"
                />
              </div>
              <div>
                <label className="label">Academic Year</label>
                <select className="input">
                  <option value="2024-2025">2024-2025</option>
                  <option value="2025-2026">2025-2026</option>
                </select>
              </div>
              <div>
                <label className="label">Current Semester</label>
                <select className="input">
                  <option value="1">Semester 1</option>
                  <option value="2">Semester 2</option>
                </select>
              </div>
            </div>
          </div>

          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">System Preferences</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Email Notifications</h4>
                  <p className="text-sm text-gray-500">Send email notifications for important events</p>
                </div>
                <input type="checkbox" className="toggle" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Auto Backup</h4>
                  <p className="text-sm text-gray-500">Automatically backup data daily</p>
                </div>
                <input type="checkbox" className="toggle" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Maintenance Mode</h4>
                  <p className="text-sm text-gray-500">Enable maintenance mode for system updates</p>
                </div>
                <input type="checkbox" className="toggle" />
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button className="btn-primary flex items-center space-x-2">
              <Save className="h-4 w-4" />
              <span>Save Settings</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
