import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { settingsAPI } from '../../utils/api';
import { toast } from 'react-hot-toast';
import { Plus, Edit2, Trash2, Save, X } from 'lucide-react';

export default function AdminSettings() {
  const [activeTab, setActiveTab] = useState('grades');
  const [editingGrade, setEditingGrade] = useState(null);
  const [editingSection, setEditingSection] = useState(null);
  const [editingSubject, setEditingSubject] = useState(null);
  const [newGrade, setNewGrade] = useState('');
  const [newSection, setNewSection] = useState('');
  const [newSubject, setNewSubject] = useState('');
  const queryClient = useQueryClient();

  // Get settings from API
  const { data: settingsData, isLoading, error } = useQuery({
    queryKey: ['settings'],
    queryFn: () => settingsAPI.getSettings(),
  });

  const settings = settingsData?.data || {};
  const grades = settings.grades || [];
  const sections = settings.sections || [];
  const subjects = settings.subjects || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="text-center py-8">
          <div className="text-red-500 mb-2">Error loading settings</div>
          <div className="text-sm text-gray-500">{error.message}</div>
        </div>
      </div>
    );
  }

  // Mutations for updating settings
  const updateGradesMutation = useMutation({
    mutationFn: (newGrades) => settingsAPI.updateGrades(newGrades),
    onSuccess: () => {
      queryClient.invalidateQueries(['settings']);
      toast.success('Grades updated successfully');
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Failed to update grades');
    },
  });

  const updateSectionsMutation = useMutation({
    mutationFn: (newSections) => settingsAPI.updateSections(newSections),
    onSuccess: () => {
      queryClient.invalidateQueries(['settings']);
      toast.success('Sections updated successfully');
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Failed to update sections');
    },
  });

  const updateSubjectsMutation = useMutation({
    mutationFn: (newSubjects) => settingsAPI.updateSubjects(newSubjects),
    onSuccess: () => {
      queryClient.invalidateQueries(['settings']);
      toast.success('Subjects updated successfully');
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Failed to update subjects');
    },
  });

  // Helper functions
  const addGrade = () => {
    if (newGrade.trim() && !grades.includes(newGrade.trim())) {
      const updatedGrades = [...grades, newGrade.trim()];
      updateGradesMutation.mutate(updatedGrades);
      setNewGrade('');
    }
  };

  const deleteGrade = (gradeToDelete) => {
    const updatedGrades = grades.filter(grade => grade !== gradeToDelete);
    updateGradesMutation.mutate(updatedGrades);
  };

  const addSection = () => {
    if (newSection.trim() && !sections.includes(newSection.trim())) {
      const updatedSections = [...sections, newSection.trim()];
      updateSectionsMutation.mutate(updatedSections);
      setNewSection('');
    }
  };

  const deleteSection = (sectionToDelete) => {
    const updatedSections = sections.filter(section => section !== sectionToDelete);
    updateSectionsMutation.mutate(updatedSections);
  };

  const addSubject = () => {
    if (newSubject.trim() && !subjects.includes(newSubject.trim())) {
      const updatedSubjects = [...subjects, newSubject.trim()];
      updateSubjectsMutation.mutate(updatedSubjects);
      setNewSubject('');
    }
  };

  const deleteSubject = (subjectToDelete) => {
    const updatedSubjects = subjects.filter(subject => subject !== subjectToDelete);
    updateSubjectsMutation.mutate(updatedSubjects);
  };

  const tabs = [
    { id: 'grades', label: 'Grades' },
    { id: 'sections', label: 'Sections' },
    { id: 'subjects', label: 'Subjects' },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Manage system configuration</p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Grades Tab */}
      {activeTab === 'grades' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Manage Grades</h3>
          
          {/* Add New Grade */}
          <div className="flex gap-2 mb-6">
            <input
              type="text"
              value={newGrade}
              onChange={(e) => setNewGrade(e.target.value)}
              placeholder="Add new grade (e.g., 9, 10, 11, 12)"
              className="input flex-1"
              onKeyPress={(e) => e.key === 'Enter' && addGrade()}
            />
            <button
              onClick={addGrade}
              disabled={!newGrade.trim() || updateGradesMutation.isLoading}
              className="btn-primary flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add Grade
            </button>
          </div>

          {/* Current Grades */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">Current Grades ({grades.length})</h4>
            {grades.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No grades configured yet. Add your first grade above.
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {grades.map((grade) => (
                  <div key={grade} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">{grade}</span>
                    <button
                      onClick={() => deleteGrade(grade)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Sections Tab */}
      {activeTab === 'sections' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Manage Sections</h3>
          
          {/* Add New Section */}
          <div className="flex gap-2 mb-6">
            <input
              type="text"
              value={newSection}
              onChange={(e) => setNewSection(e.target.value)}
              placeholder="Add new section (e.g., A, B, C)"
              className="input flex-1"
              onKeyPress={(e) => e.key === 'Enter' && addSection()}
            />
            <button
              onClick={addSection}
              disabled={!newSection.trim() || updateSectionsMutation.isLoading}
              className="btn-primary flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add Section
            </button>
          </div>

          {/* Current Sections */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">Current Sections ({sections.length})</h4>
            {sections.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No sections configured yet. Add your first section above.
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {sections.map((section) => (
                  <div key={section} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">{section}</span>
                    <button
                      onClick={() => deleteSection(section)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Subjects Tab */}
      {activeTab === 'subjects' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Manage Subjects</h3>
          
          {/* Add New Subject */}
          <div className="flex gap-2 mb-6">
            <input
              type="text"
              value={newSubject}
              onChange={(e) => setNewSubject(e.target.value)}
              placeholder="Add new subject (e.g., Mathematics, English, Physics)"
              className="input flex-1"
              onKeyPress={(e) => e.key === 'Enter' && addSubject()}
            />
            <button
              onClick={addSubject}
              disabled={!newSubject.trim() || updateSubjectsMutation.isLoading}
              className="btn-primary flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add Subject
            </button>
          </div>

          {/* Current Subjects */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">Current Subjects ({subjects.length})</h4>
            {subjects.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No subjects configured yet. Add your first subject above.
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {subjects.map((subject) => (
                  <div key={subject} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">{subject}</span>
                    <button
                      onClick={() => deleteSubject(subject)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
