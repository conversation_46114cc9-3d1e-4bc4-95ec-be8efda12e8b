import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { settingsAPI } from '../../utils/api';

export default function AdminSettingsSimple() {
  console.log('Simple Admin Settings component rendering...');

  const { data: settingsData, isLoading, error } = useQuery({
    queryKey: ['settings'],
    queryFn: () => {
      console.log('Settings API call...');
      return settingsAPI.getSettings();
    },
  });

  console.log('Settings data:', settingsData);
  console.log('Loading:', isLoading);
  console.log('Error:', error);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Loading settings...</p>
        </div>
        <div className="card">
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <div className="mt-2 text-gray-500">Loading...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Error loading settings</p>
        </div>
        <div className="card">
          <div className="text-center py-8">
            <div className="text-red-500 mb-2">Error loading settings</div>
            <div className="text-sm text-gray-500">{error.message}</div>
          </div>
        </div>
      </div>
    );
  }

  const settings = settingsData?.data || {};

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Simple settings page</p>
      </div>

      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Settings Data</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-900">Grades ({settings.grades?.length || 0})</h4>
            <p className="text-sm text-gray-600">
              {settings.grades?.join(', ') || 'None'}
            </p>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900">Sections ({settings.sections?.length || 0})</h4>
            <p className="text-sm text-gray-600">
              {settings.sections?.join(', ') || 'None'}
            </p>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900">Subjects ({settings.subjects?.length || 0})</h4>
            <p className="text-sm text-gray-600">
              {settings.subjects?.slice(0, 5).join(', ') || 'None'}
              {settings.subjects?.length > 5 && '...'}
            </p>
          </div>
        </div>
      </div>

      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Raw Data</h3>
        <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto">
          {JSON.stringify(settingsData, null, 2)}
        </pre>
      </div>
    </div>
  );
}
