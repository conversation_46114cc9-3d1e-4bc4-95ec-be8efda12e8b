import React from 'react';
import { useAuth } from '../../contexts/AuthContext';

export default function TeacherDashboardSimple() {
  const { user } = useAuth();

  console.log('Simple Teacher Dashboard - User:', user);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.name || 'Teacher'}!
        </h1>
        <p className="text-gray-600">Teacher Dashboard - Simple Version</p>
      </div>

      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">User Information</h3>
        <div className="space-y-2">
          <p><strong>Name:</strong> {user?.name || 'N/A'}</p>
          <p><strong>Email:</strong> {user?.email || 'N/A'}</p>
          <p><strong>Role:</strong> {user?.role || 'N/A'}</p>
          <p><strong>Teacher ID:</strong> {user?.teacher?.id || 'N/A'}</p>
          <p><strong>Subject:</strong> {user?.teacher?.subject || 'N/A'}</p>
        </div>
      </div>

      {!user && (
        <div className="card">
          <div className="text-center py-8">
            <div className="text-red-500">No user data available</div>
          </div>
        </div>
      )}

      {user && !user.teacher && (
        <div className="card">
          <div className="text-center py-8">
            <div className="text-red-500">No teacher profile data available</div>
          </div>
        </div>
      )}
    </div>
  );
}
