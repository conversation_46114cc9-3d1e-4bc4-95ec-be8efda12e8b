import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { classesAPI, attendanceAPI, studentsAPI } from '../../utils/api';
import { toast } from 'react-hot-toast';
import { Calendar, Users, Check, X, Clock, Save, RefreshCw } from 'lucide-react';

export default function TeacherAttendance() {
  const { user } = useAuth();
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [attendanceData, setAttendanceData] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const queryClient = useQueryClient();

  // Get teacher's classes
  const { data: classesData, isLoading: classesLoading } = useQuery({
    queryKey: ['teacher-classes', user?.teacher?.id],
    queryFn: () => classesAPI.getByTeacher(user.teacher.id),
    enabled: !!user?.teacher?.id,
  });

  const classes = classesData?.classes || [];

  // Get students for selected class
  const { data: studentsData, isLoading: studentsLoading } = useQuery({
    queryKey: ['class-students', selectedClass],
    queryFn: () => {
      const classInfo = classes.find(c => c.id.toString() === selectedClass);
      if (classInfo) {
        return studentsAPI.getAll({ grade: classInfo.grade, section: classInfo.section });
      }
      return { students: [] };
    },
    enabled: !!selectedClass && classes.length > 0,
  });

  // Get existing attendance for selected class and date
  const { data: existingAttendance, isLoading: attendanceLoading } = useQuery({
    queryKey: ['class-attendance', selectedClass, selectedDate],
    queryFn: () => attendanceAPI.getByClass(selectedClass, { date: selectedDate }),
    enabled: !!selectedClass && !!selectedDate,
  });

  const students = studentsData?.students || [];

  // Initialize attendance data when students or existing attendance changes
  useEffect(() => {
    if (students.length > 0) {
      const initialData = {};
      students.forEach(student => {
        const existingRecord = existingAttendance?.attendance?.find(
          record => record.student_id === student.id
        );
        initialData[student.id] = {
          status: existingRecord?.status || 'unmarked',
          notes: existingRecord?.notes || ''
        };
      });
      setAttendanceData(initialData);
    }
  }, [students, existingAttendance]);

  // Mark attendance mutation
  const markAttendanceMutation = useMutation({
    mutationFn: async (attendanceRecords) => {
      const results = [];
      for (const record of attendanceRecords) {
        try {
          const result = await attendanceAPI.markAttendance(record);
          results.push({ success: true, record, result });
        } catch (error) {
          results.push({ success: false, record, error });
        }
      }
      return results;
    },
    onSuccess: (results) => {
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      if (failed === 0) {
        toast.success(`Attendance marked successfully for ${successful} students`);
      } else if (successful === 0) {
        toast.error(`Failed to mark attendance for all ${failed} students`);
      } else {
        toast.success(`Attendance marked for ${successful} students. ${failed} failed.`);
      }

      // Refresh data
      queryClient.invalidateQueries(['class-attendance', selectedClass, selectedDate]);
      queryClient.invalidateQueries(['class-students', selectedClass]);
    },
    onError: (error) => {
      toast.error('Failed to mark attendance');
      console.error('Attendance error:', error);
    },
  });

  const handleStatusChange = (studentId, status) => {
    setAttendanceData(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        status
      }
    }));
  };

  const handleNotesChange = (studentId, notes) => {
    setAttendanceData(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        notes
      }
    }));
  };

  const handleSubmitAttendance = async () => {
    if (!selectedClass) {
      toast.error('Please select a class');
      return;
    }

    const attendanceRecords = Object.entries(attendanceData)
      .filter(([_, data]) => data.status !== 'unmarked')
      .map(([studentId, data]) => ({
        student_id: parseInt(studentId),
        class_id: parseInt(selectedClass),
        date: selectedDate,
        status: data.status,
        notes: data.notes
      }));

    if (attendanceRecords.length === 0) {
      toast.error('Please mark attendance for at least one student');
      return;
    }

    setIsSubmitting(true);
    try {
      await markAttendanceMutation.mutateAsync(attendanceRecords);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleMarkAllPresent = () => {
    const updatedData = {};
    students.forEach(student => {
      updatedData[student.id] = {
        ...attendanceData[student.id],
        status: 'present'
      };
    });
    setAttendanceData(prev => ({ ...prev, ...updatedData }));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'present': return 'bg-green-100 text-green-800 border-green-200';
      case 'absent': return 'bg-red-100 text-red-800 border-red-200';
      case 'late': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'present': return <Check className="w-4 h-4" />;
      case 'absent': return <X className="w-4 h-4" />;
      case 'late': return <Clock className="w-4 h-4" />;
      default: return null;
    }
  };

  const getAttendanceStats = () => {
    const total = students.length;
    const marked = Object.values(attendanceData).filter(data => data.status !== 'unmarked').length;
    const present = Object.values(attendanceData).filter(data => data.status === 'present').length;
    const absent = Object.values(attendanceData).filter(data => data.status === 'absent').length;
    const late = Object.values(attendanceData).filter(data => data.status === 'late').length;

    return { total, marked, present, absent, late };
  };

  const stats = getAttendanceStats();

  if (classesLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Mark Attendance</h1>
        <p className="text-gray-600">Record student attendance for your classes</p>
      </div>

      {/* Class and Date Selection */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Class
            </label>
            <select
              value={selectedClass}
              onChange={(e) => setSelectedClass(e.target.value)}
              className="input"
            >
              <option value="">Choose a class...</option>
              {classes.map((cls) => (
                <option key={cls.id} value={cls.id}>
                  {cls.name} - Grade {cls.grade}{cls.section} ({cls.subject})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date
            </label>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="input"
            />
          </div>

          <div className="flex items-end">
            <button
              onClick={handleMarkAllPresent}
              disabled={!selectedClass || students.length === 0}
              className="btn-secondary flex items-center gap-2"
            >
              <Users className="w-4 h-4" />
              Mark All Present
            </button>
          </div>
        </div>
      </div>

      {/* Attendance Stats */}
      {selectedClass && students.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="card text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-600">Total Students</div>
          </div>
          <div className="card text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.marked}</div>
            <div className="text-sm text-gray-600">Marked</div>
          </div>
          <div className="card text-center">
            <div className="text-2xl font-bold text-green-600">{stats.present}</div>
            <div className="text-sm text-gray-600">Present</div>
          </div>
          <div className="card text-center">
            <div className="text-2xl font-bold text-red-600">{stats.absent}</div>
            <div className="text-sm text-gray-600">Absent</div>
          </div>
          <div className="card text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.late}</div>
            <div className="text-sm text-gray-600">Late</div>
          </div>
        </div>
      )}

      {/* Student List */}
      {selectedClass && (
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Students ({students.length})
            </h3>
            {studentsLoading && (
              <RefreshCw className="w-5 h-5 animate-spin text-gray-400" />
            )}
          </div>

          {studentsLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : students.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No students found in this class
            </div>
          ) : (
            <div className="space-y-3">
              {students.map((student) => (
                <div key={student.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{student.name}</h4>
                      <p className="text-sm text-gray-600">
                        ID: {student.student_id} • Grade {student.grade}{student.section}
                      </p>
                    </div>

                    <div className="flex items-center gap-3">
                      {/* Status Buttons */}
                      <div className="flex gap-1">
                        {['present', 'absent', 'late'].map((status) => (
                          <button
                            key={status}
                            onClick={() => handleStatusChange(student.id, status)}
                            className={`px-3 py-1 rounded-md text-sm font-medium border transition-colors ${
                              attendanceData[student.id]?.status === status
                                ? getStatusColor(status)
                                : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
                            }`}
                          >
                            <div className="flex items-center gap-1">
                              {getStatusIcon(status)}
                              {status.charAt(0).toUpperCase() + status.slice(1)}
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Notes */}
                  <div className="mt-3">
                    <input
                      type="text"
                      placeholder="Add notes (optional)"
                      value={attendanceData[student.id]?.notes || ''}
                      onChange={(e) => handleNotesChange(student.id, e.target.value)}
                      className="input text-sm"
                    />
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Submit Button */}
          {students.length > 0 && (
            <div className="mt-6 flex justify-end">
              <button
                onClick={handleSubmitAttendance}
                disabled={isSubmitting || stats.marked === 0}
                className="btn-primary flex items-center gap-2"
              >
                {isSubmitting ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                Save Attendance ({stats.marked}/{stats.total})
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
