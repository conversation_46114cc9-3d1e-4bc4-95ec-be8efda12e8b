import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { classesAPI, attendanceAPI, scoresAPI } from '../../utils/api';
import { Calendar, Users, Award, BookOpen, Clock, CheckCircle, TrendingUp } from 'lucide-react';

export default function TeacherDashboard() {
  const { user } = useAuth();

  // Use teacher ID directly from auth context
  const teacherId = user?.teacher?.id;

  const { data: classesData } = useQuery({
    queryKey: ['teacher-classes', teacherId],
    queryFn: () => classesAPI.getByTeacher(teacherId),
    enabled: !!teacherId,
  });

  const { data: recentAttendance } = useQuery({
    queryKey: ['teacher-recent-attendance', teacherId],
    queryFn: () => attendanceAPI.getReport({
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    }),
    enabled: !!teacherId,
  });

  const classes = classesData?.data?.classes || [];
  const attendance = recentAttendance?.data?.report || [];

  const quickStats = [
    {
      name: 'My Classes',
      value: classes.length,
      icon: BookOpen,
      color: 'bg-blue-500',
      description: 'Active classes',
    },
    {
      name: 'Total Students',
      value: classes.reduce((sum, cls) => sum + (cls.student_count || 0), 0),
      icon: Users,
      color: 'bg-green-500',
      description: 'Across all classes',
    },
    {
      name: 'Attendance Today',
      value: attendance.length > 0 ? `${Math.round(attendance.reduce((sum, a) => sum + (a.attendance_rate || 0), 0) / attendance.length)}%` : 'N/A',
      icon: Calendar,
      color: 'bg-purple-500',
      description: 'Average attendance',
    },
    {
      name: 'Pending Scores',
      value: teacherData?.pending_scores || '0',
      icon: Award,
      color: 'bg-orange-500',
      description: 'Tests to grade',
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Welcome back, {user?.name}!</h1>
        <p className="text-gray-600">Teacher Dashboard - Manage your classes and students</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {quickStats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`${stat.color} p-3 rounded-lg`}>
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd>
                      <div className="text-2xl font-semibold text-gray-900">
                        {stat.value}
                      </div>
                      <div className="text-xs text-gray-500">
                        {stat.description}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Classes */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">My Classes</h3>
          <div className="space-y-3">
            {classes.length > 0 ? (
              classes.map((classItem) => (
                <div key={classItem.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{classItem.name}</div>
                    <div className="text-xs text-gray-500">
                      Grade {classItem.grade} - Section {classItem.section} • {classItem.subject}
                    </div>
                    <div className="text-xs text-gray-500">
                      {classItem.student_count || 0} students
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button className="btn-outline text-xs px-2 py-1">
                      <Calendar className="h-3 w-3 mr-1" />
                      Attendance
                    </button>
                    <button className="btn-outline text-xs px-2 py-1">
                      <Award className="h-3 w-3 mr-1" />
                      Scores
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4">
                No classes assigned yet
              </div>
            )}
          </div>
        </div>

        {/* Today's Schedule */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Today's Schedule</h3>
          <div className="space-y-3">
            {classes.length > 0 ? (
              classes.slice(0, 3).map((classItem, index) => (
                <div key={classItem.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div>
                    <div className="text-sm font-medium text-blue-900">
                      {classItem.subject} - Grade {classItem.grade}{classItem.section}
                    </div>
                    <div className="text-xs text-blue-700">{classItem.room || 'Room TBA'}</div>
                  </div>
                  <div className="text-xs text-blue-600 flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {classItem.time || 'Time TBA'}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4">
                <Clock className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <p>No classes scheduled for today</p>
                <p className="text-xs">Check back later for updates</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {teacherData?.recent_activities && teacherData.recent_activities.length > 0 ? (
            teacherData.recent_activities.slice(0, 3).map((activity, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  activity.type === 'attendance' ? 'bg-green-400' :
                  activity.type === 'scores' ? 'bg-blue-400' : 'bg-purple-400'
                }`}></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.description}</p>
                  <p className="text-xs text-gray-500">{activity.time_ago}</p>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center text-gray-500 py-4">
              <TrendingUp className="mx-auto h-8 w-8 text-gray-400 mb-2" />
              <p>No recent activity</p>
              <p className="text-xs">Your activities will appear here</p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="btn-outline text-left p-4">
            <Calendar className="h-6 w-6 text-blue-600 mb-2" />
            <div className="text-sm font-medium text-gray-900">Mark Attendance</div>
            <div className="text-xs text-gray-500">Take today's attendance</div>
          </button>
          <button className="btn-outline text-left p-4">
            <Award className="h-6 w-6 text-green-600 mb-2" />
            <div className="text-sm font-medium text-gray-900">Add Scores</div>
            <div className="text-xs text-gray-500">Enter test results</div>
          </button>
          <button className="btn-outline text-left p-4">
            <Users className="h-6 w-6 text-purple-600 mb-2" />
            <div className="text-sm font-medium text-gray-900">View Students</div>
            <div className="text-xs text-gray-500">Manage student records</div>
          </button>
          <button className="btn-outline text-left p-4">
            <TrendingUp className="h-6 w-6 text-orange-600 mb-2" />
            <div className="text-sm font-medium text-gray-900">Generate Report</div>
            <div className="text-xs text-gray-500">Class performance report</div>
          </button>
        </div>
      </div>
    </div>
  );
}
