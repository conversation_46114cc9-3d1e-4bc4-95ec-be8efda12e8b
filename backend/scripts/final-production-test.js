const axios = require('axios');

async function finalProductionTest() {
  console.log('🎯 FINAL PRODUCTION TEST - ALL CRITICAL FEATURES\n');

  const baseURL = 'http://localhost:3001/api';
  let allPassed = true;

  try {
    // Test 1: Admin Login and Settings
    console.log('1️⃣ Admin Login and Settings...');
    const adminLogin = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    console.log('   ✅ Admin login successful');
    
    const adminHeaders = { headers: { 'Authorization': `Bearer ${adminLogin.data.token}` } };
    const settings = await axios.get(`${baseURL}/settings`, adminHeaders);
    console.log(`   ✅ Settings loaded: ${settings.data.data.grades?.length} grades, ${settings.data.data.sections?.length} sections, ${settings.data.data.subjects?.length} subjects`);

    // Test 2: Teacher Login and Dashboard Data
    console.log('\n2️⃣ Teacher Login and Dashboard...');
    const teacherLogin = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'teacher123'
    });
    
    const teacherUser = teacherLogin.data.user;
    console.log(`   ✅ Teacher login: ${teacherUser.name} (ID: ${teacherUser.teacher.id})`);
    
    const teacherHeaders = { headers: { 'Authorization': `Bearer ${teacherLogin.data.token}` } };
    const teacherClasses = await axios.get(`${baseURL}/classes/teacher/${teacherUser.teacher.id}`, teacherHeaders);
    console.log(`   ✅ Teacher classes: ${teacherClasses.data.classes.length} classes assigned`);

    // Test 3: Attendance Flow
    console.log('\n3️⃣ Attendance System...');
    if (teacherClasses.data.classes.length > 0) {
      const testClass = teacherClasses.data.classes[0];
      const students = await axios.get(`${baseURL}/students?grade=${testClass.grade}&section=${testClass.section}`, teacherHeaders);
      
      if (students.data.students.length > 0) {
        const testStudent = students.data.students[0];
        
        // Mark attendance
        const attendanceData = {
          student_id: testStudent.id,
          class_id: testClass.id,
          date: '2025-06-24',
          status: 'present',
          notes: 'Final test'
        };
        
        await axios.post(`${baseURL}/attendance/mark`, attendanceData, teacherHeaders);
        console.log('   ✅ Attendance marked successfully');
        
        // Verify attendance
        const attendance = await axios.get(`${baseURL}/attendance/class/${testClass.id}?date=2025-06-24`, teacherHeaders);
        const studentRecord = attendance.data.attendance.find(r => r.student_id === testStudent.student_id);
        
        if (studentRecord) {
          console.log(`   ✅ Attendance verified: ${studentRecord.student_name} - ${studentRecord.status}`);
        } else {
          console.log('   ❌ Attendance verification failed');
          allPassed = false;
        }
      }
    }

    // Test 4: Student Login
    console.log('\n4️⃣ Student Login...');
    const studentLogin = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'student123'
    });
    
    const studentUser = studentLogin.data.user;
    console.log(`   ✅ Student login: ${studentUser.name} (ID: ${studentUser.student.id})`);

    // Test 5: Data Management
    console.log('\n5️⃣ Data Management...');
    const allStudents = await axios.get(`${baseURL}/students`, adminHeaders);
    const allTeachers = await axios.get(`${baseURL}/teachers`, adminHeaders);
    const allClasses = await axios.get(`${baseURL}/classes`, adminHeaders);
    
    console.log(`   ✅ Students: ${allStudents.data.students.length} total`);
    console.log(`   ✅ Teachers: ${allTeachers.data.data.teachers.length} total`);
    console.log(`   ✅ Classes: ${allClasses.data.classes.length} total`);

    // Test 6: Security
    console.log('\n6️⃣ Security Tests...');
    try {
      await axios.get(`${baseURL}/settings`, { headers: { 'Authorization': `Bearer ${teacherLogin.data.token}` } });
      console.log('   ❌ Security breach: Teacher accessed admin settings');
      allPassed = false;
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('   ✅ Security working: Teacher blocked from admin settings');
      } else {
        console.log(`   ⚠️ Unexpected error: ${error.response?.status}`);
      }
    }

    console.log('\n' + '='.repeat(60));
    if (allPassed) {
      console.log('🎉 ALL TESTS PASSED - SYSTEM IS PRODUCTION READY! 🚀');
      console.log('\n✅ Features Verified:');
      console.log('   • Admin authentication and settings management');
      console.log('   • Teacher authentication with profile data');
      console.log('   • Teacher class assignment and dashboard');
      console.log('   • Attendance marking and retrieval');
      console.log('   • Student authentication with profile data');
      console.log('   • Data management (students, teachers, classes)');
      console.log('   • Role-based security controls');
      console.log('\n🚀 Ready for production deployment!');
    } else {
      console.log('❌ SOME TESTS FAILED - REVIEW REQUIRED');
    }

  } catch (error) {
    console.error('\n💥 CRITICAL ERROR:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    allPassed = false;
  }

  return allPassed;
}

finalProductionTest();
