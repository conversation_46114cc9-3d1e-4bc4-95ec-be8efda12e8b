const axios = require('axios');

async function testSettingsAPI() {
  console.log('🧪 TESTING SETTINGS API\n');

  const baseURL = 'http://localhost:3001/api';
  
  try {
    // Test admin login
    console.log('1️⃣ Testing Admin Login...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    console.log('✅ Admin login successful');
    const token = loginResponse.data.token;
    const authHeaders = { headers: { 'Authorization': `Bearer ${token}` } };

    // Test settings API
    console.log('\n2️⃣ Testing Settings API...');
    const settingsResponse = await axios.get(`${baseURL}/settings`, authHeaders);
    
    console.log('✅ Settings API successful');
    console.log('📋 Settings response structure:');
    console.log(JSON.stringify(settingsResponse.data, null, 2));
    
    const settings = settingsResponse.data.data;
    console.log('\n📊 Settings Analysis:');
    console.log(`   - Grades: ${settings.grades?.length || 0} items`);
    console.log(`   - Sections: ${settings.sections?.length || 0} items`);
    console.log(`   - Subjects: ${settings.subjects?.length || 0} items`);
    
    if (settings.grades) {
      console.log(`   - Grades list: [${settings.grades.join(', ')}]`);
    }
    if (settings.sections) {
      console.log(`   - Sections list: [${settings.sections.join(', ')}]`);
    }
    if (settings.subjects) {
      console.log(`   - Subjects list: [${settings.subjects.slice(0, 5).join(', ')}${settings.subjects.length > 5 ? '...' : ''}]`);
    }

    console.log('\n🎉 SETTINGS API TEST COMPLETED SUCCESSFULLY!');

  } catch (error) {
    console.error('\n❌ SETTINGS API TEST FAILED:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Full response:', JSON.stringify(error.response.data, null, 2));
    }
    if (error.response?.status) {
      console.error('Status code:', error.response.status);
    }
  }
}

testSettingsAPI();
