const axios = require('axios');

async function comprehensiveProductionTest() {
  console.log('🚀 COMPREHENSIVE PRODUCTION READINESS TEST\n');
  console.log('Testing all critical features for production deployment...\n');

  const baseURL = 'http://localhost:3001/api';
  let testResults = {
    passed: 0,
    failed: 0,
    tests: []
  };

  function logTest(name, success, details = '') {
    const status = success ? '✅' : '❌';
    console.log(`${status} ${name}${details ? ' - ' + details : ''}`);
    testResults.tests.push({ name, success, details });
    if (success) testResults.passed++;
    else testResults.failed++;
  }

  try {
    // Test 1: Admin Authentication
    console.log('🔐 AUTHENTICATION TESTS');
    const adminLogin = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    logTest('Admin Login', adminLogin.status === 200, 'Admin can authenticate');
    const adminToken = adminLogin.data.token;
    const adminHeaders = { headers: { 'Authorization': `Bearer ${adminToken}` } };

    // Test 2: Teacher Authentication
    const teacherLogin = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'teacher123'
    });
    logTest('Teacher Login', teacherLogin.status === 200 && teacherLogin.data.user.teacher, 
      `Teacher profile included: ${!!teacherLogin.data.user.teacher}`);
    const teacherToken = teacherLogin.data.token;
    const teacherHeaders = { headers: { 'Authorization': `Bearer ${teacherToken}` } };
    const teacherId = teacherLogin.data.user.teacher.id;

    // Test 3: Student Authentication
    const studentLogin = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'student123'
    });
    logTest('Student Login', studentLogin.status === 200 && studentLogin.data.user.student,
      `Student profile included: ${!!studentLogin.data.user.student}`);
    const studentToken = studentLogin.data.token;
    const studentHeaders = { headers: { 'Authorization': `Bearer ${studentToken}` } };

    console.log('\n📚 CORE FUNCTIONALITY TESTS');

    // Test 4: Settings Management
    const settings = await axios.get(`${baseURL}/settings`, adminHeaders);
    logTest('Settings API', settings.status === 200 && settings.data.data.grades,
      `${settings.data.data.grades?.length || 0} grades, ${settings.data.data.sections?.length || 0} sections`);

    // Test 5: Teacher Classes
    const teacherClasses = await axios.get(`${baseURL}/classes/teacher/${teacherId}`, teacherHeaders);
    logTest('Teacher Classes', teacherClasses.status === 200,
      `${teacherClasses.data.classes.length} classes assigned`);

    // Test 6: Student Management
    const students = await axios.get(`${baseURL}/students`, adminHeaders);
    logTest('Student Management', students.status === 200,
      `${students.data.students.length} students in system`);

    // Test 7: Teacher Management
    const teachers = await axios.get(`${baseURL}/teachers`, adminHeaders);
    logTest('Teacher Management', teachers.status === 200,
      `${teachers.data.data.teachers.length} teachers in system`);

    // Test 8: Attendance System
    if (teacherClasses.data.classes.length > 0) {
      const testClass = teacherClasses.data.classes[0];
      const classStudents = await axios.get(`${baseURL}/students?grade=${testClass.grade}&section=${testClass.section}`, teacherHeaders);
      
      if (classStudents.data.students.length > 0) {
        const testStudent = classStudents.data.students[0];
        const attendanceData = {
          student_id: testStudent.id,
          class_id: testClass.id,
          date: '2025-06-24',
          status: 'present',
          notes: 'Production test'
        };
        
        const markAttendance = await axios.post(`${baseURL}/attendance/mark`, attendanceData, teacherHeaders);
        logTest('Attendance Marking', markAttendance.status === 200, 'Teacher can mark attendance');
        
        const getAttendance = await axios.get(`${baseURL}/attendance/class/${testClass.id}?date=2025-06-24`, teacherHeaders);
        logTest('Attendance Retrieval', getAttendance.status === 200 && getAttendance.data.attendance.length > 0,
          `${getAttendance.data.attendance.length} attendance records found`);
      }
    }

    // Test 9: Score Management
    const scores = await axios.get(`${baseURL}/scores`, adminHeaders);
    logTest('Score Management', scores.status === 200, 'Score system accessible');

    // Test 10: Reports
    const reports = await axios.get(`${baseURL}/reports/overview`, adminHeaders);
    logTest('Reports System', reports.status === 200, 'Reports generation working');

    console.log('\n🔒 SECURITY TESTS');

    // Test 11: Unauthorized Access
    try {
      await axios.get(`${baseURL}/admin/users`);
      logTest('Unauthorized Access Protection', false, 'Should require authentication');
    } catch (error) {
      logTest('Unauthorized Access Protection', error.response?.status === 401 || error.response?.status === 404,
        'Properly blocks unauthorized access');
    }

    // Test 12: Role-based Access
    try {
      await axios.get(`${baseURL}/settings`, teacherHeaders);
      logTest('Role-based Access Control', false, 'Teacher should not access admin settings');
    } catch (error) {
      logTest('Role-based Access Control', error.response?.status === 403,
        'Properly enforces role restrictions');
    }

    console.log('\n📊 DATA INTEGRITY TESTS');

    // Test 13: Data Consistency
    const allClasses = await axios.get(`${baseURL}/classes`, adminHeaders);
    const allStudentsCheck = await axios.get(`${baseURL}/students`, adminHeaders);
    const allTeachersCheck = await axios.get(`${baseURL}/teachers`, adminHeaders);
    
    logTest('Data Consistency', 
      allClasses.status === 200 && allStudentsCheck.status === 200 && allTeachersCheck.status === 200,
      'All core data endpoints accessible');

    console.log('\n📈 PERFORMANCE TESTS');

    // Test 14: Response Times
    const start = Date.now();
    await axios.get(`${baseURL}/students`, adminHeaders);
    const responseTime = Date.now() - start;
    logTest('Response Time', responseTime < 1000, `${responseTime}ms (should be < 1000ms)`);

    console.log('\n🎯 PRODUCTION READINESS SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Tests Passed: ${testResults.passed}`);
    console.log(`❌ Tests Failed: ${testResults.failed}`);
    console.log(`📊 Success Rate: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);
    
    if (testResults.failed === 0) {
      console.log('\n🎉 ALL TESTS PASSED - SYSTEM IS PRODUCTION READY! 🚀');
    } else {
      console.log('\n⚠️  SOME TESTS FAILED - REVIEW REQUIRED BEFORE PRODUCTION');
      console.log('\nFailed Tests:');
      testResults.tests.filter(t => !t.success).forEach(test => {
        console.log(`   ❌ ${test.name}: ${test.details}`);
      });
    }

  } catch (error) {
    console.error('\n💥 CRITICAL ERROR DURING TESTING:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

comprehensiveProductionTest();
