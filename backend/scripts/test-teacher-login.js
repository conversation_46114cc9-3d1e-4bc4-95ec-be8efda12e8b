const axios = require('axios');

async function testTeacherLogin() {
  console.log('🧪 TESTING TEACHER LOGIN FLOW\n');

  const baseURL = 'http://localhost:3001/api';
  
  try {
    // Test teacher login
    console.log('1️⃣ Testing Teacher Login...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'teacher123'
    });
    
    console.log('✅ Login successful');
    console.log('📋 Response data structure:');
    console.log(JSON.stringify(loginResponse.data, null, 2));
    
    const user = loginResponse.data.user;
    console.log('\n📊 User Analysis:');
    console.log(`   - ID: ${user.id}`);
    console.log(`   - Name: ${user.name}`);
    console.log(`   - Email: ${user.email}`);
    console.log(`   - Role: ${user.role}`);
    console.log(`   - Teacher data present: ${!!user.teacher}`);
    
    if (user.teacher) {
      console.log(`   - Teacher ID: ${user.teacher.id}`);
      console.log(`   - Teacher Subject: ${user.teacher.subject}`);
      console.log(`   - Teacher Experience: ${user.teacher.experience}`);
    } else {
      console.log('   ❌ Teacher data missing from user object');
    }

    // Test teacher classes API
    if (user.teacher?.id) {
      console.log('\n2️⃣ Testing Teacher Classes API...');
      const token = loginResponse.data.token;
      const authHeaders = { headers: { 'Authorization': `Bearer ${token}` } };
      
      const classesResponse = await axios.get(`${baseURL}/classes/teacher/${user.teacher.id}`, authHeaders);
      console.log(`✅ Classes API successful: ${classesResponse.data.classes.length} classes found`);
      
      classesResponse.data.classes.forEach((cls, index) => {
        console.log(`   ${index + 1}. ${cls.name} - Grade ${cls.grade}${cls.section} (${cls.student_count || 0} students)`);
      });
    }

    console.log('\n🎉 TEACHER LOGIN TEST COMPLETED SUCCESSFULLY!');

  } catch (error) {
    console.error('\n❌ TEACHER LOGIN TEST FAILED:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Full response:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testTeacherLogin();
