const express = require('express');
const router = express.Router();
const attendanceController = require('../controllers/attendanceController');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// Mark attendance (teachers and admin only)
router.post('/mark', authorizeRoles('admin', 'teacher'), validate(schemas.markAttendance), attendanceController.markAttendance);

// Bulk mark attendance (teachers and admin only)
router.post('/mark/bulk', authorizeRoles('admin', 'teacher'), attendanceController.markBulkAttendance);

// Get attendance by student
router.get('/student/:studentId', attendanceController.getAttendanceByStudent);

// Get attendance by class (teachers and admin only)
router.get('/class/:classId', authorizeRoles('admin', 'teacher'), attendanceController.getAttendanceByClass);

// Get attendance report (admin and teachers)
router.get('/report', authorizeRoles('admin', 'teacher'), attendanceController.getAttendanceReport);

// Delete attendance record (admin only)
router.delete('/:id', authorizeRoles('admin'), attendanceController.deleteAttendance);

module.exports = router;
