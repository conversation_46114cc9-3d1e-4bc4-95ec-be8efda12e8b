const Attendance = require('../models/Attendance');
const Student = require('../models/Student');

const markAttendance = async (req, res) => {
  try {
    const { student_id, class_id, date, status, notes } = req.body;
    const marked_by = req.user.id;

    const attendance = Attendance.markAttendance({
      student_id,
      class_id,
      date,
      status,
      marked_by,
      notes
    });

    res.json({
      message: 'Attendance marked successfully',
      attendance
    });

  } catch (error) {
    console.error('Mark attendance error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const markBulkAttendance = async (req, res) => {
  try {
    const { attendanceRecords } = req.body;
    const marked_by = req.user.id;

    if (!Array.isArray(attendanceRecords) || attendanceRecords.length === 0) {
      return res.status(400).json({ error: 'Invalid attendance records' });
    }

    const results = [];
    const errors = [];

    for (const record of attendanceRecords) {
      try {
        const { student_id, class_id, date, status, notes } = record;

        // Validate required fields
        if (!student_id || !class_id || !date || !status) {
          errors.push({ record, error: 'Missing required fields' });
          continue;
        }

        const attendance = Attendance.markAttendance({
          student_id,
          class_id,
          date,
          status,
          marked_by,
          notes
        });

        results.push(attendance);
      } catch (error) {
        errors.push({ record, error: error.message });
      }
    }

    res.json({
      message: `Bulk attendance processed. ${results.length} successful, ${errors.length} failed.`,
      successful: results.length,
      failed: errors.length,
      results,
      errors
    });

  } catch (error) {
    console.error('Bulk mark attendance error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getAttendanceByStudent = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { startDate, endDate, class_id } = req.query;

    // Check if user can access this student's data
    if (req.user.role === 'student') {
      const student = Student.findByUserId(req.user.id);
      if (!student || student.id.toString() !== studentId) {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    const filters = {};
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;
    if (class_id) filters.class_id = class_id;

    const attendance = Attendance.findByStudent(studentId, filters);
    
    res.json({ attendance });

  } catch (error) {
    console.error('Get student attendance error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getAttendanceByClass = async (req, res) => {
  try {
    const { classId } = req.params;
    const { date, startDate, endDate } = req.query;

    const filters = {};
    if (date) filters.date = date;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const attendance = Attendance.findByClass(classId, filters);
    
    res.json({ attendance });

  } catch (error) {
    console.error('Get class attendance error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getAttendanceReport = async (req, res) => {
  try {
    const { grade, section, startDate, endDate } = req.query;

    const filters = {};
    if (grade) filters.grade = grade;
    if (section) filters.section = section;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const report = Attendance.getAttendanceReport(filters);
    
    res.json({ report });

  } catch (error) {
    console.error('Get attendance report error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const deleteAttendance = async (req, res) => {
  try {
    const { id } = req.params;

    const attendance = Attendance.findById(id);
    if (!attendance) {
      return res.status(404).json({ error: 'Attendance record not found' });
    }

    const deleted = Attendance.delete(id);
    if (!deleted) {
      return res.status(400).json({ error: 'Failed to delete attendance record' });
    }

    res.json({ message: 'Attendance record deleted successfully' });

  } catch (error) {
    console.error('Delete attendance error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  markAttendance,
  getAttendanceByStudent,
  getAttendanceByClass,
  getAttendanceReport,
  deleteAttendance
};
